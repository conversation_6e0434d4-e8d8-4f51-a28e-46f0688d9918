/**
 * @file test_ad9959_migration.c
 * @brief AD9959模块移植验证测试程序
 * @details 验证AD9959模块从旧GPIO操作到HAL库的移植是否成功
 * <AUTHOR>
 * @date 2025-08-01
 */

#include "AD9959.h"
#include "main.h"

/**
 * @brief AD9959移植测试主函数
 * @details 测试AD9959模块的基本功能，验证移植是否成功
 */
void test_ad9959_migration(void)
{
    // 初始化AD9959
    AD9959_Init();
    
    // 测试单通道输出设置
    // 通道0: 1MHz, 0度相位, 500mVpp幅度
    AD9959_Single_Output(0, 1000000, 0, 500);
    
    // 通道1: 2MHz, 90度相位, 400mVpp幅度
    AD9959_Single_Output(1, 2000000, 4096, 400);
    
    // 通道2: 5MHz, 180度相位, 300mVpp幅度
    AD9959_Single_Output(2, 5000000, 8192, 300);
    
    // 通道3: 10MHz, 270度相位, 200mVpp幅度
    AD9959_Single_Output(3, 10000000, 12288, 200);
    
    // 更新IO，使设置生效
    AD9959_IO_UpDate();
    
    // 测试单独设置频率
    AD9959_Set_Fre(0, 500000);  // 通道0设置为500kHz
    
    // 测试单独设置幅度
    AD9959_Set_Amp(1, 600);     // 通道1设置为600mVpp
    
    // 测试单独设置相位
    AD9959_Set_Phase(2, 0);     // 通道2设置为0度相位
    
    // 再次更新IO
    IO_Update();
}

/**
 * @brief 测试AD9959扫频功能
 */
void test_ad9959_sweep(void)
{
    // 测试频率扫描
    // 通道0从1MHz扫描到10MHz，步进100kHz，上升时间10us，下降时间5us，幅度500mVpp，相位0度
    AD9959_SetFre_Sweep(0, 1000000, 10000000, 100000, 100000, 10, 5, 500, 0);
    
    // 测试幅度扫描
    // 通道1从100mVpp扫描到500mVpp，步进50mVpp，频率5MHz，相位0度
    AD9959_SetAmp_Sweep(1, 100, 500, 50, 50, 10, 5, 5000000, 0);
    
    // 测试相位扫描
    // 通道2从0度扫描到360度，步进45度，频率1MHz，幅度300mVpp
    AD9959_SetPhase_Sweep(2, 0, 16383, 2048, 2048, 10, 5, 1000000, 300);
}

/**
 * @brief 验证GPIO操作是否正常
 */
void test_gpio_operations(void)
{
    // 测试CS信号
    CS_HIGH();
    CS_LOW();
    
    // 测试SCLK信号
    SCLK_HIGH();
    SCLK_LOW();
    
    // 测试UPDATE信号
    UPDATE_HIGH();
    UPDATE_LOW();
    
    // 测试复位信号
    Reset_HIGH();
    Reset_LOW();
    
    // 测试电源控制
    AD9959_PWR_HIGH();
    AD9959_PWR_LOW();
    
    // 测试Profile选择信号
    PS0_HIGH();
    PS0_LOW();
    PS1_HIGH();
    PS1_LOW();
    PS2_HIGH();
    PS2_LOW();
    PS3_HIGH();
    PS3_LOW();
    
    // 测试SDIO信号
    SDIO0_HIGH();
    SDIO0_LOW();
    SDIO1_HIGH();
    SDIO1_LOW();
    SDIO2_HIGH();
    SDIO2_LOW();
    SDIO3_HIGH();
    SDIO3_LOW();
}

/**
 * @brief 主测试函数
 */
void run_ad9959_migration_test(void)
{
    // 测试GPIO操作
    test_gpio_operations();
    
    // 测试基本功能
    test_ad9959_migration();
    
    // 测试扫频功能
    test_ad9959_sweep();
}
