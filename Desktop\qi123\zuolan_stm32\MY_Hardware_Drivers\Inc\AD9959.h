#ifndef _AD9959_H_
#define _AD9959_H_
#include "main.h"
#include "stdint.h"

//AD9959�ܽź궨��
//AD9959引脚宏定义 - 使用HAL库操作，对应main.h中的引脚配置
// 写操作宏定义
#define CS_HIGH()		HAL_GPIO_WritePin(AD9959_CS_GPIO_Port, AD9959_CS_Pin, GPIO_PIN_SET)
#define CS_LOW()		HAL_GPIO_WritePin(AD9959_CS_GPIO_Port, AD9959_CS_Pin, GPIO_PIN_RESET)

#define SCLK_HIGH()		HAL_GPIO_WritePin(AD9959_SCK_GPIO_Port, AD9959_SCK_Pin, GPIO_PIN_SET)
#define SCLK_LOW()		HAL_GPIO_WritePin(AD9959_SCK_GPIO_Port, AD9959_SCK_Pin, GPIO_PIN_RESET)

#define UPDATE_HIGH()	HAL_GPIO_WritePin(AD9959_UP_GPIO_Port, AD9959_UP_Pin, GPIO_PIN_SET)
#define UPDATE_LOW()	HAL_GPIO_WritePin(AD9959_UP_GPIO_Port, AD9959_UP_Pin, GPIO_PIN_RESET)

#define PS0_HIGH()		HAL_GPIO_WritePin(AD9959_P0_GPIO_Port, AD9959_P0_Pin, GPIO_PIN_SET)
#define PS0_LOW()		HAL_GPIO_WritePin(AD9959_P0_GPIO_Port, AD9959_P0_Pin, GPIO_PIN_RESET)

#define PS1_HIGH()		HAL_GPIO_WritePin(AD9959_P1_GPIO_Port, AD9959_P1_Pin, GPIO_PIN_SET)
#define PS1_LOW()		HAL_GPIO_WritePin(AD9959_P1_GPIO_Port, AD9959_P1_Pin, GPIO_PIN_RESET)

#define PS2_HIGH()		HAL_GPIO_WritePin(AD9959_P2_GPIO_Port, AD9959_P2_Pin, GPIO_PIN_SET)
#define PS2_LOW()		HAL_GPIO_WritePin(AD9959_P2_GPIO_Port, AD9959_P2_Pin, GPIO_PIN_RESET)

#define PS3_HIGH()		HAL_GPIO_WritePin(AD9959_P3_GPIO_Port, AD9959_P3_Pin, GPIO_PIN_SET)
#define PS3_LOW()		HAL_GPIO_WritePin(AD9959_P3_GPIO_Port, AD9959_P3_Pin, GPIO_PIN_RESET)

#define SDIO0_HIGH()	HAL_GPIO_WritePin(AD9959_SDIO0_GPIO_Port, AD9959_SDIO0_Pin, GPIO_PIN_SET)
#define SDIO0_LOW()		HAL_GPIO_WritePin(AD9959_SDIO0_GPIO_Port, AD9959_SDIO0_Pin, GPIO_PIN_RESET)

#define SDIO1_HIGH()	HAL_GPIO_WritePin(AD9959_SDIO1_GPIO_Port, AD9959_SDIO1_Pin, GPIO_PIN_SET)
#define SDIO1_LOW()		HAL_GPIO_WritePin(AD9959_SDIO1_GPIO_Port, AD9959_SDIO1_Pin, GPIO_PIN_RESET)

#define SDIO2_HIGH()	HAL_GPIO_WritePin(AD9959_SDIO2_GPIO_Port, AD9959_SDIO2_Pin, GPIO_PIN_SET)
#define SDIO2_LOW()		HAL_GPIO_WritePin(AD9959_SDIO2_GPIO_Port, AD9959_SDIO2_Pin, GPIO_PIN_RESET)

#define SDIO3_HIGH()	HAL_GPIO_WritePin(AD9959_SDIO3_GPIO_Port, AD9959_SDIO3_Pin, GPIO_PIN_SET)
#define SDIO3_LOW()		HAL_GPIO_WritePin(AD9959_SDIO3_GPIO_Port, AD9959_SDIO3_Pin, GPIO_PIN_RESET)

#define AD9959_PWR_HIGH()	HAL_GPIO_WritePin(AD9959_PDC_GPIO_Port, AD9959_PDC_Pin, GPIO_PIN_SET)
#define AD9959_PWR_LOW()	HAL_GPIO_WritePin(AD9959_PDC_GPIO_Port, AD9959_PDC_Pin, GPIO_PIN_RESET)

#define Reset_HIGH()	HAL_GPIO_WritePin(AD9959_RST_GPIO_Port, AD9959_RST_Pin, GPIO_PIN_SET)
#define Reset_LOW()		HAL_GPIO_WritePin(AD9959_RST_GPIO_Port, AD9959_RST_Pin, GPIO_PIN_RESET)

// 为了兼容原有代码，保留原有的宏定义方式（读写兼容）
#define CS			(HAL_GPIO_ReadPin(AD9959_CS_GPIO_Port, AD9959_CS_Pin) ? 1 : 0)
#define SCLK		(HAL_GPIO_ReadPin(AD9959_SCK_GPIO_Port, AD9959_SCK_Pin) ? 1 : 0)
#define UPDATE		(HAL_GPIO_ReadPin(AD9959_UP_GPIO_Port, AD9959_UP_Pin) ? 1 : 0)
#define PS0			(HAL_GPIO_ReadPin(AD9959_P0_GPIO_Port, AD9959_P0_Pin) ? 1 : 0)
#define PS1			(HAL_GPIO_ReadPin(AD9959_P1_GPIO_Port, AD9959_P1_Pin) ? 1 : 0)
#define PS2			(HAL_GPIO_ReadPin(AD9959_P2_GPIO_Port, AD9959_P2_Pin) ? 1 : 0)
#define PS3			(HAL_GPIO_ReadPin(AD9959_P3_GPIO_Port, AD9959_P3_Pin) ? 1 : 0)
#define SDIO0		(HAL_GPIO_ReadPin(AD9959_SDIO0_GPIO_Port, AD9959_SDIO0_Pin) ? 1 : 0)
#define SDIO1		(HAL_GPIO_ReadPin(AD9959_SDIO1_GPIO_Port, AD9959_SDIO1_Pin) ? 1 : 0)
#define SDIO2		(HAL_GPIO_ReadPin(AD9959_SDIO2_GPIO_Port, AD9959_SDIO2_Pin) ? 1 : 0)
#define SDIO3		(HAL_GPIO_ReadPin(AD9959_SDIO3_GPIO_Port, AD9959_SDIO3_Pin) ? 1 : 0)
#define AD9959_PWR	(HAL_GPIO_ReadPin(AD9959_PDC_GPIO_Port, AD9959_PDC_Pin) ? 1 : 0)
#define Reset		(HAL_GPIO_ReadPin(AD9959_RST_GPIO_Port, AD9959_RST_Pin) ? 1 : 0)

//AD9959�Ĵ�����ַ����
#define CSR_ADD   0x00   //CSR ͨ��ѡ��Ĵ���
#define FR1_ADD   0x01   //FR1 ���ܼĴ���1
#define FR2_ADD   0x02   //FR2 ���ܼĴ���2
#define CFR_ADD   0x03   //CFR ͨ�����ܼĴ���

#define CFTW0_ADD 0x04   //CTW0 ͨ��Ƶ��ת���ּĴ���
#define CPOW0_ADD 0x05   //CPW0 ͨ����λת���ּĴ���
#define ACR_ADD   0x06   //ACR ���ȿ��ƼĴ���

#define LSRR_ADD  0x07   //LSR ����ɨ��б�ʼĴ���
#define RDW_ADD   0x08   //RDW ����ɨ�������Ĵ���
#define FDW_ADD   0x09   //FDW �½�ɨ�������Ĵ���

#define PROFILE_ADDR_BASE   0x0A   //Profile�Ĵ���,�����ļ��Ĵ�����ʼ��ַ

//CSR[7:4]ͨ��ѡ������λ
#define CH0 0x10
#define CH1 0x20
#define CH2 0x40
#define CH3 0x80

//FR1[9:8] ���Ƶ�ƽѡ��λ
#define LEVEL_MOD_2  	0x00//2��ƽ���� 2�׵���
#define LEVEL_MOD_4		0x01//4��ƽ����	4�׵���
#define LEVEL_MOD_8		0x02//8��ƽ����	8�׵���
#define LEVEL_MOD_16	0x03//16��ƽ����	16�׵���

//CFR[23:22]  ��Ƶ��λ��AFP��ѡ��λ
#define	DISABLE_Mod		0x00	//00	�����ѽ���
#define	ASK 					0x40	//01	������ƣ����Ƽ���
#define	FSK 					0x80	//10	Ƶ�ʵ��ƣ�Ƶ�Ƽ���
#define	PSK 					0xc0	//11	��λ���ƣ����Ƽ���

//��CFR[14]������ɨ������ sweep enable
#define	SWEEP_ENABLE	0x40	//1	����
#define	SWEEP_DISABLE	0x00	//0	������

void delay1 (uint32_t length);//��ʱ
void IntReset(void);	 			//AD9959��λ
void IO_Update(void); 		  //AD9959��������
void Intserve(void);				//IO�ڵ�ƽ״̬��ʼ��
void AD9959_Init(void);			//IO�ڳ�ʼ��

/***********************AD9959�����Ĵ�����������*****************************************/
void AD9959_WriteData(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData);//��AD9959д����
void Write_CFTW0(uint32_t fre);										//дCFTW0ͨ��Ƶ��ת���ּĴ���
void Write_ACR(uint16_t Ampli);										//дACRͨ������ת���ּĴ���
void Write_CPOW0(uint16_t Phase);									//дCPOW0ͨ����λת���ּĴ���

void Write_LSRR(uint8_t rsrr,uint8_t fsrr);				//дLSRR����ɨ��б�ʼĴ���
void Write_RDW(uint32_t r_delta);									//дRDW���������Ĵ���
void Write_FDW(uint32_t f_delta);									//дFDW�½������Ĵ���

void Write_Profile_Fre(uint8_t profile,uint32_t data);//дProfile�Ĵ���,Ƶ��
void Write_Profile_Ampli(uint8_t profile,uint16_t data);//дProfile�Ĵ���,����
void Write_Profile_Phase(uint8_t profile,uint16_t data);//дProfile�Ĵ���,��λ
/********************************************************************************************/


/*****************************��Ƶ��������***********************************/
void AD9959_Set_Fre(uint8_t Channel,uint32_t Freq); //дƵ��
void AD9959_Set_Amp(uint8_t Channel, uint16_t Ampli);//д����
void AD9959_Set_Phase(uint8_t Channel,uint16_t Phase);//д��λ
/****************************************************************************/

/*****************************���Ʋ�������  ***********************************/
void AD9959_Modulation_Init(uint8_t Channel,uint8_t Modulation,uint8_t Sweep_en,uint8_t Nlevel);//���ø���ͨ���ĵ���ģʽ��
void AD9959_SetFSK(uint8_t Channel, uint32_t *data,uint16_t Phase);//����FSK���ƵĲ���
void AD9959_SetASK(uint8_t Channel, uint16_t *data,uint32_t fre,uint16_t Phase);//����ASK���ƵĲ���
void AD9959_SetPSK(uint8_t Channel, uint16_t *data,uint32_t Freq);//����PSK���ƵĲ���

void AD9959_SetFre_Sweep(uint8_t Channel, uint32_t s_data,uint32_t e_data,uint32_t r_delta,uint32_t f_delta,uint8_t rsrr,uint8_t fsrr,uint16_t Ampli,uint16_t Phase);//��������ɨƵ�Ĳ���
void AD9959_SetAmp_Sweep(uint8_t Channel, uint32_t s_Ampli,uint16_t e_Ampli,uint32_t r_delta,uint32_t f_delta,uint8_t rsrr,uint8_t fsrr,uint32_t fre,uint16_t Phase);//��������ɨ���Ĳ���
void AD9959_SetPhase_Sweep(uint8_t Channel, uint16_t s_data,uint16_t e_data,uint16_t r_delta,uint16_t f_delta,uint8_t rsrr,uint8_t fsrr,uint32_t fre,uint16_t Ampli);//相位扫描的参数
/********************************************************************************************/

/*****************************便捷函数接口***********************************/
void AD9959_Single_Output(uint8_t Channel, uint32_t Freq, uint16_t Phase, uint16_t Ampli); // 单通道输出设置
void AD9959_IO_UpDate(void); // IO更新函数别名
/********************************************************************************************/

#endif








